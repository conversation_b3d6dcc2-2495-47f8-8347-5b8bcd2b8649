package limits

// SubscriptionTier represents a user's subscription level
type SubscriptionTier string

const (
	TierFree SubscriptionTier = "free"
	TierPro  SubscriptionTier = "pro"
)

// ResourceType represents the type of resource being limited
type ResourceType string

const (
	ResourcePlayers ResourceType = "players"
	ResourceSeasons ResourceType = "seasons"
	ResourceMatches ResourceType = "matches"
	ResourceTeams   ResourceType = "teams"
)

// ResourceLimits defines the limits for each resource type per subscription tier
type ResourceLimits struct {
	Players int
	Seasons int
	Matches int
	Teams   int
}

// GetLimitsForTier returns the resource limits for a given subscription tier
func GetLimitsForTier(tier SubscriptionTier) ResourceLimits {
	switch tier {
	case TierFree:
		return ResourceLimits{
			Players: 100,
			Seasons: 30,
			Matches: 730,
			Teams:   15,
		}
	case TierPro:
		return ResourceLimits{
			Players: -1, // -1 means unlimited
			Seasons: -1,
			Matches: -1,
			Teams:   -1,
		}
	default:
		// Default to free tier limits
		return ResourceLimits{
			Players: 100,
			Seasons: 30,
			Matches: 730,
			Teams:   15,
		}
	}
}

// IsUnlimited checks if a limit is unlimited (-1)
func (rl ResourceLimits) IsUnlimited(resourceType ResourceType) bool {
	switch resourceType {
	case ResourcePlayers:
		return rl.Players == -1
	case ResourceSeasons:
		return rl.Seasons == -1
	case ResourceMatches:
		return rl.Matches == -1
	case ResourceTeams:
		return rl.Teams == -1
	default:
		return false
	}
}

// GetLimit returns the limit for a specific resource type
func (rl ResourceLimits) GetLimit(resourceType ResourceType) int {
	switch resourceType {
	case ResourcePlayers:
		return rl.Players
	case ResourceSeasons:
		return rl.Seasons
	case ResourceMatches:
		return rl.Matches
	case ResourceTeams:
		return rl.Teams
	default:
		return 0
	}
}

// LimitCheckResult represents the result of a limit check
type LimitCheckResult struct {
	Allowed      bool
	CurrentCount int
	Limit        int
	ResourceType ResourceType
	IsUnlimited  bool
	ErrorMessage string
}

// IsLimitExceeded checks if the current count exceeds the limit
func (lcr LimitCheckResult) IsLimitExceeded() bool {
	return !lcr.Allowed
}

// RemainingCapacity calculates how many more resources can be created
func (lcr LimitCheckResult) RemainingCapacity() int {
	if lcr.IsUnlimited {
		return -1 // Unlimited
	}
	remaining := lcr.Limit - lcr.CurrentCount
	if remaining < 0 {
		return 0
	}
	return remaining
}
